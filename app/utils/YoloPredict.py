import os
import json
import subprocess
import tempfile
import shutil
from PIL import Image

from app.views.ViewsBase import g_logger
from app.models import TaskSample
from datetime import datetime


class YoloPredict:
    """YOLO模型预测工具类"""
    
    @staticmethod
    def predict_task_samples(task, model_path, storage_dir):
        """
        使用YOLO模型对任务中的所有样本进行预测，并将结果保存为标注
        
        Args:
            task: 任务对象
            model_path: YOLO模型文件路径
            storage_dir: 存储根目录
            
        Returns:
            tuple: (success, message, info)
        """
        try:
            # 验证模型文件是否存在
            if not os.path.exists(model_path):
                return False, f"模型文件不存在: {model_path}", {}
            
            # 验证模型文件格式
            if not model_path.endswith('.pt'):
                return False, "只支持 .pt 格式的YOLO模型文件", {}
            
            # 获取任务的所有样本
            samples = TaskSample.objects.filter(task_code=task.code)
            if not samples:
                return False, "任务中没有样本数据", {}
            
            # 创建临时目录用于预测
            temp_dir = tempfile.mkdtemp(prefix="yolo_predict_")
            images_dir = os.path.join(temp_dir, "images")
            os.makedirs(images_dir, exist_ok=True)
            
            try:
                # 复制图片到临时目录
                sample_paths = {}  # 存储样本代码和图片路径的映射
                for sample in samples:
                    src_image_path = os.path.join(storage_dir, "task", task.code, "sample", sample.new_filename)
                    if os.path.exists(src_image_path):
                        dst_image_path = os.path.join(images_dir, sample.new_filename)
                        shutil.copy(src_image_path, dst_image_path)
                        sample_paths[sample.new_filename] = sample.code
                
                if not sample_paths:
                    return False, "没有找到有效的图片文件", {}
                
                # 执行YOLO预测
                predict_dir = os.path.join(temp_dir, "predict")
                success, msg = YoloPredict._run_yolo_predict(model_path, images_dir, predict_dir)
                if not success:
                    return False, msg, {}
                
                # 解析预测结果并保存为标注
                success, msg, info = YoloPredict._parse_and_save_predictions(
                    task, predict_dir, sample_paths, storage_dir
                )
                
                return success, msg, info
                
            finally:
                # 清理临时目录
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass
                    
        except Exception as e:
            g_logger.error(f"YOLO预测失败: {str(e)}")
            return False, f"预测过程中发生错误: {str(e)}", {}
    
    @staticmethod
    def _run_yolo_predict(model_path, source_dir, output_dir):
        """
        运行YOLO预测命令
        
        Args:
            model_path: 模型文件路径
            source_dir: 输入图片目录
            output_dir: 输出目录
            
        Returns:
            tuple: (success, message)
        """
        try:
            # 构建YOLO预测命令
            command = [
                "yolo", "detect", "predict",
                f"model={model_path}",
                f"source={source_dir}",
                f"project={output_dir}",
                "save_txt=True",  # 保存txt格式的标注文件
                "save_conf=True"  # 保存置信度
            ]
            
            g_logger.info(f"执行YOLO预测命令: {' '.join(command)}")
            
            # 执行命令
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0:
                g_logger.info("YOLO预测执行成功")
                return True, "预测成功"
            else:
                error_msg = result.stderr or result.stdout or "未知错误"
                g_logger.error(f"YOLO预测失败: {error_msg}")
                return False, f"YOLO预测失败: {error_msg}"
                
        except subprocess.TimeoutExpired:
            return False, "预测超时，请检查模型和数据"
        except FileNotFoundError:
            return False, "未找到YOLO命令，请确保已正确安装YOLO"
        except Exception as e:
            g_logger.error(f"执行YOLO预测时发生错误: {str(e)}")
            return False, f"执行预测时发生错误: {str(e)}"
    
    @staticmethod
    def _parse_and_save_predictions(task, predict_dir, sample_paths, storage_dir):
        """
        解析YOLO预测结果并保存为标注
        
        Args:
            task: 任务对象
            predict_dir: 预测结果目录
            sample_paths: 样本路径映射
            storage_dir: 存储根目录
            
        Returns:
            tuple: (success, message, info)
        """
        try:
            # 查找预测结果目录（YOLO会创建一个predict子目录）
            predict_result_dir = None
            for item in os.listdir(predict_dir):
                item_path = os.path.join(predict_dir, item)
                if os.path.isdir(item_path) and "predict" in item:
                    predict_result_dir = item_path
                    break
            
            if not predict_result_dir:
                return False, "未找到预测结果目录", {}
            
            # 查找labels目录
            labels_dir = os.path.join(predict_result_dir, "labels")
            if not os.path.exists(labels_dir):
                return False, "未找到预测标注文件", {}
            
            # 先删除所有现有标注
            TaskSample.objects.filter(task_code=task.code).update(
                annotation_state=0,
                annotation_content=None,
                annotation_time=None,
                annotation_user_id=None,
                annotation_username=None
            )
            
            # 解析预测结果并收集所有使用的类别
            processed_count = 0
            annotation_count = 0
            all_class_ids = set()  # 收集所有预测中使用的类别ID

            for filename in os.listdir(labels_dir):
                if filename.endswith('.txt'):
                    # 获取对应的图片文件名
                    image_filename = filename.replace('.txt', '.jpg')
                    if image_filename not in sample_paths:
                        # 尝试其他常见格式
                        for ext in ['.png', '.jpeg', '.JPG', '.PNG', '.JPEG']:
                            test_filename = filename.replace('.txt', ext)
                            if test_filename in sample_paths:
                                image_filename = test_filename
                                break

                    if image_filename not in sample_paths:
                        continue

                    sample_code = sample_paths[image_filename]
                    label_file_path = os.path.join(labels_dir, filename)

                    # 读取标注文件并收集类别ID
                    annotations, class_ids = YoloPredict._parse_yolo_label_file(
                        label_file_path, image_filename, storage_dir, task.code
                    )

                    # 收集所有类别ID
                    all_class_ids.update(class_ids)

                    if annotations:
                        # 保存标注到数据库
                        sample = TaskSample.objects.filter(code=sample_code).first()
                        if sample:
                            sample.annotation_content = json.dumps(annotations)
                            sample.annotation_state = 1
                            sample.annotation_time = datetime.now()
                            sample.annotation_user_id = 0  # 系统自动标注
                            sample.annotation_username = "模型预标注"
                            sample.save()
                            annotation_count += len(annotations)

                    processed_count += 1

            # 更新任务的标签列表
            YoloPredict._update_task_labels(task, all_class_ids)
            
            info = {
                "processed_count": processed_count,
                "annotation_count": annotation_count,
                "total_samples": len(sample_paths)
            }
            
            return True, f"预标注完成！处理了 {processed_count} 个样本，生成了 {annotation_count} 个标注", info
            
        except Exception as e:
            g_logger.error(f"解析预测结果失败: {str(e)}")
            return False, f"解析预测结果失败: {str(e)}", {}
    
    @staticmethod
    def _parse_yolo_label_file(label_file_path, image_filename, storage_dir, task_code):
        """
        解析YOLO标注文件，转换为系统标注格式

        Args:
            label_file_path: 标注文件路径
            image_filename: 图片文件名
            storage_dir: 存储根目录
            task_code: 任务代码

        Returns:
            tuple: (标注数据列表, 类别ID集合)
        """
        try:
            # 获取图片尺寸
            image_path = os.path.join(storage_dir, "task", task_code, "sample", image_filename)
            if not os.path.exists(image_path):
                return [], set()

            with Image.open(image_path) as img:
                image_width, image_height = img.size

            annotations = []
            class_ids = set()  # 收集所有类别ID

            with open(label_file_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue

                    parts = line.split()
                    if len(parts) < 5:
                        continue

                    try:
                        class_id = int(parts[0])
                        x_center = float(parts[1])
                        y_center = float(parts[2])
                        width = float(parts[3])
                        height = float(parts[4])
                        confidence = float(parts[5]) if len(parts) > 5 else 0.5

                        # 收集类别ID
                        class_ids.add(class_id)

                        # 转换为绝对坐标
                        x_center_abs = x_center * image_width
                        y_center_abs = y_center * image_height
                        width_abs = width * image_width
                        height_abs = height * image_height

                        # 计算边界框坐标
                        x1 = x_center_abs - width_abs / 2
                        y1 = y_center_abs - height_abs / 2
                        x2 = x_center_abs + width_abs / 2
                        y2 = y_center_abs + height_abs / 2

                        # 构建标注数据（矩形格式）
                        annotation = {
                            "labelName": f"class_{class_id}",  # 默认标签名
                            "labelColor": "#ff0000",  # 默认红色
                            "content": [
                                {"x": x1, "y": y1},
                                {"x": x2, "y": y1},
                                {"x": x2, "y": y2},
                                {"x": x1, "y": y2}
                            ],
                            "contentType": "rect",
                            "confidence": confidence
                        }

                        annotations.append(annotation)

                    except (ValueError, IndexError) as e:
                        g_logger.warning(f"解析标注行失败: {line}, 错误: {str(e)}")
                        continue

            return annotations, class_ids

        except Exception as e:
            g_logger.error(f"解析YOLO标注文件失败: {str(e)}")
            return [], set()

    @staticmethod
    def _update_task_labels(task, class_ids):
        """
        更新任务的标签列表，添加预测中使用的类别

        Args:
            task: 任务对象
            class_ids: 预测中使用的类别ID集合
        """
        try:
            # 获取现有标签
            existing_labels = []
            if task.labels:
                try:
                    existing_labels = json.loads(task.labels)
                except json.JSONDecodeError:
                    g_logger.warning(f"任务 {task.code} 的标签格式无效，将重新创建")
                    existing_labels = []

            # 获取现有标签名称集合
            existing_label_names = set()
            if existing_labels:
                for label in existing_labels:
                    if isinstance(label, dict) and 'labelName' in label:
                        existing_label_names.add(label['labelName'])

            # 为新的类别ID创建标签
            colors = ["#ff0000", "#00ff00", "#0000ff", "#ffff00", "#ff00ff", "#00ffff",
                     "#ffa500", "#800080", "#008000", "#ffc0cb"]  # 预定义颜色

            new_labels_added = False
            for class_id in sorted(class_ids):
                label_name = f"class_{class_id}"

                # 如果标签不存在，则添加
                if label_name not in existing_label_names:
                    color = colors[class_id % len(colors)]  # 循环使用颜色
                    new_label = {
                        "labelName": label_name,
                        "labelColor": color
                    }
                    existing_labels.append(new_label)
                    existing_label_names.add(label_name)
                    new_labels_added = True
                    g_logger.info(f"为任务 {task.code} 添加新标签: {label_name}")

            # 如果添加了新标签，更新任务
            if new_labels_added:
                task.labels = json.dumps(existing_labels)
                task.save()
                g_logger.info(f"任务 {task.code} 标签列表已更新，共 {len(existing_labels)} 个标签")

        except Exception as e:
            g_logger.error(f"更新任务标签失败: {str(e)}")

#!/usr/bin/env python
"""
测试模型预标注功能修复
验证 TaskSample 字段访问是否正确
"""

import os
import sys
import django

# 设置 Django 环境
sys.path.append('/Users/<USER>/Documents/Workspace/projects/ggeclabel')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'framework.settings')
django.setup()

from app.models import TaskSample, Task

def test_task_sample_fields():
    """测试 TaskSample 模型字段"""
    print("=== 测试 TaskSample 模型字段 ===")
    
    # 获取第一个样本进行测试
    sample = TaskSample.objects.first()
    if not sample:
        print("❌ 没有找到任何样本数据")
        return False
    
    print(f"✅ 找到样本: {sample.code}")
    
    # 检查字段是否存在
    fields_to_check = ['new_filename', 'old_filename', 'task_code']
    for field in fields_to_check:
        if hasattr(sample, field):
            value = getattr(sample, field)
            print(f"✅ {field}: {value}")
        else:
            print(f"❌ 字段不存在: {field}")
            return False
    
    return True

def test_file_path_structure():
    """测试文件路径结构"""
    print("\n=== 测试文件路径结构 ===")
    
    sample = TaskSample.objects.first()
    if not sample:
        print("❌ 没有找到任何样本数据")
        return False
    
    # 从配置中获取存储目录
    from app.views.ViewsBase import g_config
    storage_dir = g_config.storageDir
    
    # 构建文件路径
    file_path = os.path.join(storage_dir, "task", sample.task_code, "sample", sample.new_filename)
    print(f"📁 预期文件路径: {file_path}")
    
    if os.path.exists(file_path):
        print("✅ 文件存在")
        return True
    else:
        print("❌ 文件不存在")
        
        # 检查可能的路径
        alt_paths = [
            os.path.join(storage_dir, "task", sample.task_code, sample.new_filename),
            os.path.join(storage_dir, sample.task_code, sample.new_filename),
            os.path.join(storage_dir, "task", sample.task_code, "images", sample.new_filename),
        ]
        
        print("🔍 检查其他可能的路径:")
        for alt_path in alt_paths:
            if os.path.exists(alt_path):
                print(f"✅ 找到文件: {alt_path}")
                return True
            else:
                print(f"❌ 不存在: {alt_path}")
        
        return False

def test_yolo_predict_import():
    """测试 YoloPredict 导入"""
    print("\n=== 测试 YoloPredict 导入 ===")
    
    try:
        from app.utils.YoloPredict import YoloPredict
        print("✅ YoloPredict 导入成功")
        
        # 检查关键方法是否存在
        methods = ['predict_task_samples', '_run_yolo_predict', '_parse_and_save_predictions']
        for method in methods:
            if hasattr(YoloPredict, method):
                print(f"✅ 方法存在: {method}")
            else:
                print(f"❌ 方法不存在: {method}")
                return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试模型预标注功能修复...")
    
    tests = [
        ("TaskSample 字段测试", test_task_sample_fields),
        ("文件路径结构测试", test_file_path_structure),
        ("YoloPredict 导入测试", test_yolo_predict_import),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！模型预标注功能应该可以正常工作了。")
    else:
        print("⚠️  部分测试失败，可能需要进一步调试。")

if __name__ == "__main__":
    main()
